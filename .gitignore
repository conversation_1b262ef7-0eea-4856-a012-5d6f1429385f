#-------------------------
# Operating Specific Junk Files
#-------------------------

# OS X
.DS_Store
.AppleDouble
.LSOverride

# OS X Thumbnails
._*

# Windows image file caches
Thumbs.db
ehthumbs.db
Desktop.ini

# Recycle Bin used on file shares
$RECYCLE.BIN/

# Windows Installer files
*.cab
*.msi
*.msm
*.msp

# Windows shortcuts
*.lnk

# Linux
*~

# KDE directory preferences
.directory

# Linux trash folder which might appear on any partition or disk
.Trash-*

#-------------------------
# Environment Files
#-------------------------
# These should never be under version control,
# as it poses a security risk.
.env
.env.testing
.env.production
.env.local
.env.example

#-------------------------
# Temporary Files
#-------------------------
writable/cache/*
!writable/cache/index.html

writable/logs/*
!writable/logs/index.html

writable/session/*
!writable/session/index.html

writable/uploads/*
!writable/uploads/index.html

writable/debugbar/*
!writable/debugbar/.gitkeep

php_errors.log

#-------------------------
# User Guide Related
#-------------------------
user_guide_src/build/*
user_guide_src/cilexer/build/*
user_guide_src/cilexer/dist/*
user_guide_src/cilexer/pycilexer.egg-info/*

#-------------------------
# Test Files
#-------------------------
tests/coverage*

# Don't save phpunit under version control.
phpunit

#-------------------------
# Composer
#-------------------------
vendor/

#-------------------------
# IDE Files
#-------------------------
#-------------------------
# Sublime Text
#-------------------------
*.tmlanguage.cache
*.tmPreferences.cache
*.stTheme.cache
*.sublime-workspace
*.sublime-project
.phpintel
/api/

#-------------------------
# Visual Studio Code
#-------------------------
.vscode/

#-------------------------
# Vim
#-------------------------
*.tmp
*.swp
*.swo

#-------------------------
# PHPStorm
#-------------------------
.idea/

#-------------------------
# Atom
#-------------------------
.atom/

#-------------------------
# VSCode
#-------------------------
.vscode/

#-------------------------
# Emacs
#-------------------------
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

#-------------------------
# Eclipse
#-------------------------
.buildpath
.project
.settings/

#-------------------------
# IntelliJ
#-------------------------
*.iml
*.ipr
*.iws
.idea/

#-------------------------
# NetBeans
#-------------------------
.nb-gradle/
nbproject/private/
build/
nbbuild/
dist/
nbdist/
nbactions.xml
nb-configuration.xml

#-------------------------
# Node.js
#-------------------------
node_modules/
npm-debug.log
yarn-error.log

#-------------------------
# SASS
#-------------------------
.sass-cache/

#-------------------------
# Database backups
#-------------------------
backups/*.sql

#-------------------------
# Project specific
#-------------------------
# Uploaded files
public/uploads/*
!public/uploads/index.html

# Configuration files with sensitive data
app/Config/Database.php.backup
app/Config/Email.php.backup

# Session files
writable/session/ci_session*

# Cache files
writable/cache/*
!writable/cache/index.html

# Log files
writable/logs/*.log

# Debug files
writable/debugbar/*.json
